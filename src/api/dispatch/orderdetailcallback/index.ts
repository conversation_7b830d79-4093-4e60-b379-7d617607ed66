import request from '@/config/axios'

// 任务订单详情日志 VO
export interface TaskOrderDetailCallbackVO {
  id: number // 编号，唯一自增
  boxCode: string // 盒子号
  callbackParam: string // 回调参数
  callbackResult: string // 回调结果
  description: string // 描述
  no: string // 订单号
  notifyUrl: string // 异步通知地址
  orderDetailId: number // 订单编号
  status: number // 状态
  taskCode: string // 任务编码
  taskType: number // 任务类型
  orderDetailIds: string // 订单编号集合
}

// 任务订单详情日志 API
export const TaskOrderDetailCallbackApi = {
  // 查询任务订单详情日志分页
  getTaskOrderDetailCallbackPage: async (params: any) => {
    return await request.get({ url: `/dispatch/task-order-detail-callback/page`, params })
  },

  // 查询任务订单详情日志详情
  getTaskOrderDetailCallback: async (id: number) => {
    return await request.get({ url: `/dispatch/task-order-detail-callback/get?id=` + id })
  },

  // 新增任务订单详情日志
  createTaskOrderDetailCallback: async (data: TaskOrderDetailCallbackVO) => {
    return await request.post({ url: `/dispatch/task-order-detail-callback/create`, data })
  },

  // 修改任务订单详情日志
  updateTaskOrderDetailCallback: async (data: TaskOrderDetailCallbackVO) => {
    return await request.put({ url: `/dispatch/task-order-detail-callback/update`, data })
  },

  // 删除任务订单详情日志
  deleteTaskOrderDetailCallback: async (id: number) => {
    return await request.delete({ url: `/dispatch/task-order-detail-callback/delete?id=` + id })
  },

  // 导出任务订单详情日志 Excel
  exportTaskOrderDetailCallback: async (params) => {
    return await request.download({ url: `/dispatch/task-order-detail-callback/export-excel`, params })
  }
}
