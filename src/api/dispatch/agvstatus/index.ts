import request from '@/config/axios'

// 设备agv状态 VO
export interface AgvStatusVO {
  id: number // 编号，唯一自增
  carCode: string // AGV编号
  carDumpEnergy: string // 剩余电量
  carMsg: string // 车辆故障报警信息
  carOrientation: string // 车体角度
  carPos: string // 当前点
  carSpeed: string // 实时速度
  carStatus: string // 车辆运行状态
  containerNo: string // 容器号、托盘号
  ip: string // ip
  isLoad: string // 载货状态
  locationX: string // X坐标
  locationY: string // Y坐标
  port: string // 端口
}

// 设备agv状态 API
export const AgvStatusApi = {
  // 查询设备agv状态分页
  getAgvStatusPage: async (params: any) => {
    return await request.get({ url: `/dispatch/agvStatus/page`, params })
  },

  // 查询设备agv状态详情
  getAgvStatus: async (id: number) => {
    return await request.get({ url: `/dispatch/agvStatus/get?id=` + id })
  },

  // 新增设备agv状态
  createAgvStatus: async (data: AgvStatusVO) => {
    return await request.post({ url: `/dispatch/agvStatus/create`, data })
  },

  // 修改设备agv状态
  updateAgvStatus: async (data: AgvStatusVO) => {
    return await request.put({ url: `/dispatch/agvStatus/update`, data })
  },

  // 删除设备agv状态
  deleteAgvStatus: async (id: number) => {
    return await request.delete({ url: `/dispatch/agvStatus/delete?id=` + id })
  },

  // 导出设备agv状态 Excel
  exportAgvStatus: async (params) => {
    return await request.download({ url: `/dispatch/agvStatus/export-excel`, params })
  }
}
