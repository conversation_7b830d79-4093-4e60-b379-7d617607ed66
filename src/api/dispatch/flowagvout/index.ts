import request from '@/config/axios'

// agv大库出库盒子状态记录 VO
export interface TaskFlowAgvOutVO {
  id: number // 编号，唯一自增
  boxCode: string // 盒号
  destination: string // 样本目的站点
  executeMethod: string // 执行方法
  executeObject: string // 执行对象
  log: string // 步骤执行日志
  no: string // 流水号
  orderType: number // 订单类型 1.入库 2.出库
  remark: string // 备注
  retryCount: number // 重试次数
  scanResult: boolean // 扫描结果
  status: number // 步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中
  taskCode: string // 任务编号
}

// agv大库出库盒子状态记录 API
export const TaskFlowAgvOutApi = {
  // 查询agv大库出库盒子状态记录分页
  getTaskFlowAgvOutPage: async (params: any) => {
    return await request.get({ url: `/dispatch/task-flow-agv-out/page`, params })
  },

  // 查询agv大库出库盒子状态记录详情
  getTaskFlowAgvOut: async (id: number) => {
    return await request.get({ url: `/dispatch/task-flow-agv-out/get?id=` + id })
  },

  // 新增agv大库出库盒子状态记录
  createTaskFlowAgvOut: async (data: TaskFlowAgvOutVO) => {
    return await request.post({ url: `/dispatch/task-flow-agv-out/create`, data })
  },

  // 修改agv大库出库盒子状态记录
  updateTaskFlowAgvOut: async (data: TaskFlowAgvOutVO) => {
    return await request.put({ url: `/dispatch/task-flow-agv-out/update`, data })
  },

  // 删除agv大库出库盒子状态记录
  deleteTaskFlowAgvOut: async (id: number) => {
    return await request.delete({ url: `/dispatch/task-flow-agv-out/delete?id=` + id })
  },

  // 导出agv大库出库盒子状态记录 Excel
  exportTaskFlowAgvOut: async (params) => {
    return await request.download({ url: `/dispatch/task-flow-agv-out/export-excel`, params })
  }
}
