import request from '@/config/axios'

// 任务步骤agv日志 VO
export interface TaskFlowStepAgvVO {
  id: number // 编号，唯一自增
  boxCode: string // 盒号
  description: string // 描述
  destination: string // 样本目的站点
  endTime: Date // 步骤结束时间
  executeMethod: string // 执行方法
  executeObject: string // 执行对象
  log: string // 步骤执行日志
  no: string // 流水号
  orderDetailId: number // 任务ID，关联orderDetail表
  orderId: number // 任务ID，关联order表
  orderType: number // 订单类型 1.入库 2.出库
  retryCount: number // 重试次数
  scanResult: boolean // 扫描结果
  startTime: Date // 步骤开始时间
  status: number // 步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中
  stepName: string // 步骤名称
  taskCode: string // 任务编号
  taskType: number // 任务类型 1入 2 出
}

// 任务步骤agv日志 API
export const TaskFlowStepAgvApi = {
  // 查询任务步骤agv日志分页
  getTaskFlowStepAgvPage: async (params: any) => {
    return await request.get({ url: `/dispatch/task-flow-step-agv/page`, params })
  },

  // 查询任务步骤agv日志详情
  getTaskFlowStepAgv: async (id: number) => {
    return await request.get({ url: `/dispatch/task-flow-step-agv/get?id=` + id })
  },

  // 新增任务步骤agv日志
  createTaskFlowStepAgv: async (data: TaskFlowStepAgvVO) => {
    return await request.post({ url: `/dispatch/task-flow-step-agv/create`, data })
  },

  // 修改任务步骤agv日志
  updateTaskFlowStepAgv: async (data: TaskFlowStepAgvVO) => {
    return await request.put({ url: `/dispatch/task-flow-step-agv/update`, data })
  },

  // 删除任务步骤agv日志
  deleteTaskFlowStepAgv: async (id: number) => {
    return await request.delete({ url: `/dispatch/task-flow-step-agv/delete?id=` + id })
  },

  // 导出任务步骤agv日志 Excel
  exportTaskFlowStepAgv: async (params) => {
    return await request.download({ url: `/dispatch/task-flow-step-agv/export-excel`, params })
  },

  // 获取任务步骤agv日志更据 流水号
  getTaskFlowStepAgvByNo: async (no: string) => {
    return await request.get({ url: `/dispatch/task-flow-step-agv/get-by-no?no=` + no })
  }
}
