import request from '@/config/axios'

// 试管信息 VO
export interface TubeVO {
  boxCode: string // 盒号
  tubeCode: string // 试管编码
  coordinate: string | null // 坐标
  locationCode: string // 位置编码
}

// 盒子信息 VO
export interface BoxVO {
  boxCode: string // 盒号
  tubeList: TubeVO[] // 试管列表
  locationCode: string // 位置编码
  consumableCode: string // 耗材编码
}

// 任务作业请求 VO
export interface SendTaskJobVO {
  boxList: BoxVO[] // 盒子列表
  extInfo: string | null // 扩展信息
  taskCode: string // 任务编码
  deviceCode: string // 设备编码
  enabledAgv: number // 是否启用AGV 1：是 0：否
  taskStatus: number // 任务状态
  reserveTime: Date | null // 预约时间
  taskCategory: number // 任务类别
}

// 冷冻器任务 API
export const FreezerApi = {
  // 发送任务作业
  sendTaskJob: async (data: SendTaskJobVO) => {
    return await request.post({ url: `/dispatch/freezer/sendTaskJob`, data })
  }
}
