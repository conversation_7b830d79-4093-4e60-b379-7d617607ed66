import request from '@/config/axios'

// 任务订单详情 VO
export interface TaskOrderDetailVO {
  id: number // 编号，唯一自增
  body: string // 描述信息
  boxCode: string // 盒号
  log: string // 日志
  msg: string // 回调信息
  no: string // 订单号
  notifyUrl: string // 异步通知地址
  orderId: number // 订单编号
  station: string // 站点
  status: number // 状态
  taskCategory: number // 任务类型 1.入库 2.出库
  taskCode: string // 任务编码
  tubesCount: number // 总管数
  type: string // 订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消
  userIp: string // 用户 IP
}

// 任务订单详情 API
export const TaskOrderDetailApi = {
  // 查询任务订单详情分页
  getTaskOrderDetailPage: async (params: any) => {
    return await request.get({ url: `/dispatch/task-order-detail/page`, params })
  },

  // 查询任务订单详情详情
  getTaskOrderDetail: async (id: number) => {
    return await request.get({ url: `/dispatch/task-order-detail/get?id=` + id })
  },

  // 新增任务订单详情
  createTaskOrderDetail: async (data: TaskOrderDetailVO) => {
    return await request.post({ url: `/dispatch/task-order-detail/create`, data })
  },

  // 修改任务订单详情
  updateTaskOrderDetail: async (data: TaskOrderDetailVO) => {
    return await request.put({ url: `/dispatch/task-order-detail/update`, data })
  },

  // 删除任务订单详情
  deleteTaskOrderDetail: async (id: number) => {
    return await request.delete({ url: `/dispatch/task-order-detail/delete?id=` + id })
  },

  // 导出任务订单详情 Excel
  exportTaskOrderDetail: async (params) => {
    return await request.download({ url: `/dispatch/task-order-detail/export-excel`, params })
  },

  // 获得列表
  getTaskOrderDetailListByOrderId : async (orderId: number) => {
    return await request.get({
      url: `/dispatch/task-order-detail/list-by-order-id?orderId=` + orderId
    })
  }
}
