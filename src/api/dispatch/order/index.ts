import request from '@/config/axios'

// 任务订单 VO
export interface TaskOrderVO {
  id: number // 编号，唯一自增
  body: string // 描述信息
  boxCount: number // 总盒数
  deviceDesc: string // 设备描述
  enabledAgv: number // 是否agv 1：是 0：否
  executedBoxCount: number // 已执行盒数
  executedTubesCount: number // 已执行管数
  finishTime: Date // 任务完成时间
  isLack: number // 是否缺盒 1：缺盒 0：不缺盒
  no: string // 订单号
  orderType: number // 订单类型
  station: string // 站点
  status: number // 状态
  subject: string // 标题
  taskCode: string // 任务编码
  tubesCount: number // 总管数
  userIp: string // 用户 IP
}

// 任务订单 API
export const TaskOrderApi = {
  // 查询任务订单分页
  getTaskOrderPage: async (params: any) => {
    return await request.get({ url: `/dispatch/task-order/page`, params })
  },

  // 查询任务订单详情
  getTaskOrder: async (id: number) => {
    return await request.get({ url: `/dispatch/task-order/get?id=` + id })
  },

  // 新增任务订单
  createTaskOrder: async (data: TaskOrderVO) => {
    return await request.post({ url: `/dispatch/task-order/create`, data })
  },

  // 修改任务订单
  updateTaskOrder: async (data: TaskOrderVO) => {
    return await request.put({ url: `/dispatch/task-order/update`, data })
  },

  // 删除任务订单
  deleteTaskOrder: async (id: number) => {
    return await request.delete({ url: `/dispatch/task-order/delete?id=` + id })
  },

  // 导出任务订单 Excel
  exportTaskOrder: async (params) => {
    return await request.download({ url: `/dispatch/task-order/export-excel`, params })
  },

  // 导出任务订单 Excel
  closeTaskOrder: async (id: number) => {
    return await request.post({ url: `/dispatch/task-order/close?id=`+ id })
  }
}
