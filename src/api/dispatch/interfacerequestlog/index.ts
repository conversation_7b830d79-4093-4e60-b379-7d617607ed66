import request from '@/config/axios'

// 接口请求日志 VO
export interface InterfaceRequestLogVO {
  id: number // 编号，唯一自增
  apiName: string // 接口名称
  consumeLog: string // 消费日志
  consumeStatus: number // 消费状态 1：消费成功 2：消费失败
  exceptionInfo: string // 异常信息
  host: string // 请求host
  isLack: number // 是否缺盒 1：缺盒 0：不缺盒
  no: string // no
  remark: string // 备注
  requestKey: string // 请求key
  requestParam: string // 请求参数
  responseParam: string // 响应参数
  status: number // 推送状态 1：推送成功 2：推送失败
  taskStatus: number // 任务状态，1.下发 2.重试 4.已取消
  uniqueId: string // 唯一标识
  url: string // 请求url
}

// 接口请求日志 API
export const InterfaceRequestLogApi = {
  // 查询接口请求日志分页
  getInterfaceRequestLogPage: async (params: any) => {
    return await request.get({ url: `/dispatch/interface-request-log/page`, params })
  },

  // 查询接口请求日志详情
  getInterfaceRequestLog: async (id: number) => {
    return await request.get({ url: `/dispatch/interface-request-log/get?id=` + id })
  },

  // 新增接口请求日志
  createInterfaceRequestLog: async (data: InterfaceRequestLogVO) => {
    return await request.post({ url: `/dispatch/interface-request-log/create`, data })
  },

  // 修改接口请求日志
  updateInterfaceRequestLog: async (data: InterfaceRequestLogVO) => {
    return await request.put({ url: `/dispatch/interface-request-log/update`, data })
  },

  // 删除接口请求日志
  deleteInterfaceRequestLog: async (id: number) => {
    return await request.delete({ url: `/dispatch/interface-request-log/delete?id=` + id })
  },

  // 导出接口请求日志 Excel
  exportInterfaceRequestLog: async (params) => {
    return await request.download({ url: `/dispatch/interface-request-log/export-excel`, params })
  }
}
