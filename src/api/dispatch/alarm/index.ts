import request from '@/config/axios'

// 设备报警上传信息 VO
export interface IotAlarmVO {
  id: number // 编号，唯一自增
  code: string // 报警编码
  deal: string // 报警处理
  equipment: string // 报警设备
  level: string // 报警级别
  message: string // 报警信息
  name: string // 报警名称
  alarmTime: Date // 报警时间
}

// 设备报警上传信息 API
export const IotAlarmApi = {
  // 查询设备报警上传信息分页
  getIotAlarmPage: async (params: any) => {
    return await request.get({ url: `/dispatch/alarm/page`, params })
  },

  // 查询设备报警上传信息详情
  getIotAlarm: async (id: number) => {
    return await request.get({ url: `/dispatch/alarm/get?id=` + id })
  },

  // 新增设备报警上传信息
  createIotAlarm: async (data: IotAlarmVO) => {
    return await request.post({ url: `/dispatch/alarm/create`, data })
  },

  // 修改设备报警上传信息
  updateIotAlarm: async (data: IotAlarmVO) => {
    return await request.put({ url: `/dispatch/alarm/update`, data })
  },

  // 删除设备报警上传信息
  deleteIotAlarm: async (id: number) => {
    return await request.delete({ url: `/dispatch/alarm/delete?id=` + id })
  },

  // 导出设备报警上传信息 Excel
  exportIotAlarm: async (params) => {
    return await request.download({ url: `/dispatch/alarm/export-excel`, params })
  }
}
