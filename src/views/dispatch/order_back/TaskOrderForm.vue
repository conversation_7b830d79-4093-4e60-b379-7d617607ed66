<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="描述信息" prop="body">
        <el-input v-model="formData.body" placeholder="请输入描述信息" />
      </el-form-item>
      <el-form-item label="总盒数" prop="boxCount">
        <el-input v-model="formData.boxCount" placeholder="请输入总盒数" />
      </el-form-item>
      <el-form-item label="设备描述" prop="deviceDesc">
        <el-input v-model="formData.deviceDesc" placeholder="请输入设备描述" />
      </el-form-item>
      <el-form-item label="是否agv 1：是 0：否" prop="enabledAgv">
        <el-input v-model="formData.enabledAgv" placeholder="请输入是否agv 1：是 0：否" />
      </el-form-item>
      <el-form-item label="已执行盒数" prop="executedBoxCount">
        <el-input v-model="formData.executedBoxCount" placeholder="请输入已执行盒数" />
      </el-form-item>
      <el-form-item label="已执行管数" prop="executedTubesCount">
        <el-input v-model="formData.executedTubesCount" placeholder="请输入已执行管数" />
      </el-form-item>
      <el-form-item label="任务完成时间" prop="finishTime">
        <el-date-picker
          v-model="formData.finishTime"
          type="date"
          value-format="x"
          placeholder="选择任务完成时间"
        />
      </el-form-item>
      <el-form-item label="是否缺盒 1：缺盒 0：不缺盒" prop="isLack">
        <el-input v-model="formData.isLack" placeholder="请输入是否缺盒 1：缺盒 0：不缺盒" />
      </el-form-item>
      <el-form-item label="订单号" prop="no">
        <el-input v-model="formData.no" placeholder="请输入订单号" />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择订单类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="站点" prop="station">
        <el-input v-model="formData.station" placeholder="请输入站点" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="标题" prop="subject">
        <el-input v-model="formData.subject" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input v-model="formData.taskCode" placeholder="请输入任务编码" />
      </el-form-item>
      <el-form-item label="总管数" prop="tubesCount">
        <el-input v-model="formData.tubesCount" placeholder="请输入总管数" />
      </el-form-item>
      <el-form-item label="用户 IP" prop="userIp">
        <el-input v-model="formData.userIp" placeholder="请输入用户 IP" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TaskOrderApi, TaskOrderVO } from '@/api/dispatch/order'

/** 任务订单 表单 */
defineOptions({ name: 'TaskOrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  body: undefined,
  boxCount: undefined,
  deviceDesc: undefined,
  enabledAgv: undefined,
  executedBoxCount: undefined,
  executedTubesCount: undefined,
  finishTime: undefined,
  isLack: undefined,
  no: undefined,
  orderType: undefined,
  station: undefined,
  status: undefined,
  subject: undefined,
  taskCode: undefined,
  tubesCount: undefined,
  userIp: undefined
})
const formRules = reactive({
  boxCount: [{ required: true, message: '总盒数不能为空', trigger: 'blur' }],
  executedBoxCount: [{ required: true, message: '已执行盒数不能为空', trigger: 'blur' }],
  executedTubesCount: [{ required: true, message: '已执行管数不能为空', trigger: 'blur' }],
  isLack: [{ required: true, message: '是否缺盒 1：缺盒 0：不缺盒不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  subject: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  tubesCount: [{ required: true, message: '总管数不能为空', trigger: 'blur' }],
  userIp: [{ required: true, message: '用户 IP不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskOrderApi.getTaskOrder(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskOrderVO
    if (formType.value === 'create') {
      await TaskOrderApi.createTaskOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskOrderApi.updateTaskOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    body: undefined,
    boxCount: undefined,
    deviceDesc: undefined,
    enabledAgv: undefined,
    executedBoxCount: undefined,
    executedTubesCount: undefined,
    finishTime: undefined,
    isLack: undefined,
    no: undefined,
    orderType: undefined,
    station: undefined,
    status: undefined,
    subject: undefined,
    taskCode: undefined,
    tubesCount: undefined,
    userIp: undefined
  }
  formRef.value?.resetFields()
}
</script>
