<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="描述信息" prop="body">
        <el-input
          v-model="queryParams.body"
          placeholder="请输入描述信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="总盒数" prop="boxCount">
        <el-input
          v-model="queryParams.boxCount"
          placeholder="请输入总盒数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="设备描述" prop="deviceDesc">
        <el-input
          v-model="queryParams.deviceDesc"
          placeholder="请输入设备描述"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否agv 1：是 0：否" prop="enabledAgv">
        <el-input
          v-model="queryParams.enabledAgv"
          placeholder="请输入是否agv 1：是 0：否"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="已执行盒数" prop="executedBoxCount">
        <el-input
          v-model="queryParams.executedBoxCount"
          placeholder="请输入已执行盒数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="已执行管数" prop="executedTubesCount">
        <el-input
          v-model="queryParams.executedTubesCount"
          placeholder="请输入已执行管数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务完成时间" prop="finishTime">
        <el-date-picker
          v-model="queryParams.finishTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="是否缺盒 1：缺盒 0：不缺盒" prop="isLack">
        <el-input
          v-model="queryParams.isLack"
          placeholder="请输入是否缺盒 1：缺盒 0：不缺盒"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单号" prop="no">
        <el-input
          v-model="queryParams.no"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select
          v-model="queryParams.orderType"
          placeholder="请选择订单类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="站点" prop="station">
        <el-input
          v-model="queryParams.station"
          placeholder="请输入站点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="总管数" prop="tubesCount">
        <el-input
          v-model="queryParams.tubesCount"
          placeholder="请输入总管数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户 IP" prop="userIp">
        <el-input
          v-model="queryParams.userIp"
          placeholder="请输入用户 IP"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hv:task-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hv:task-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="描述信息" align="center" prop="body" />
      <el-table-column label="总盒数" align="center" prop="boxCount" />
      <el-table-column label="设备描述" align="center" prop="deviceDesc" />
      <el-table-column label="是否agv 1：是 0：否" align="center" prop="enabledAgv" />
      <el-table-column label="已执行盒数" align="center" prop="executedBoxCount" />
      <el-table-column label="已执行管数" align="center" prop="executedTubesCount" />
      <el-table-column
        label="任务完成时间"
        align="center"
        prop="finishTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="是否缺盒 1：缺盒 0：不缺盒" align="center" prop="isLack" />
      <el-table-column label="订单号" align="center" prop="no" />
      <el-table-column label="订单类型" align="center" prop="orderType" />
      <el-table-column label="站点" align="center" prop="station" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="标题" align="center" prop="subject" />
      <el-table-column label="任务编码" align="center" prop="taskCode" />
      <el-table-column label="总管数" align="center" prop="tubesCount" />
      <el-table-column label="用户 IP" align="center" prop="userIp" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hv:task-order:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hv:task-order:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskOrderForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskOrderApi, TaskOrderVO } from '@/api/dispatch/order'
import TaskOrderForm from './TaskOrderForm.vue'

/** 任务订单 列表 */
defineOptions({ name: 'TaskOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TaskOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  body: undefined,
  boxCount: undefined,
  deviceDesc: undefined,
  enabledAgv: undefined,
  executedBoxCount: undefined,
  executedTubesCount: undefined,
  finishTime: [],
  isLack: undefined,
  no: undefined,
  orderType: undefined,
  station: undefined,
  status: undefined,
  subject: undefined,
  taskCode: undefined,
  tubesCount: undefined,
  userIp: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskOrderApi.getTaskOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskOrderApi.deleteTaskOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskOrderApi.exportTaskOrder(queryParams)
    download.excel(data, '任务订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
