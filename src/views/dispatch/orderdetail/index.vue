<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="描述信息" prop="body">
        <el-input
          v-model="queryParams.body"
          placeholder="请输入描述信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="盒号" prop="boxCode">
        <el-input
          v-model="queryParams.boxCode"
          placeholder="请输入盒号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="日志" prop="log">
        <el-input
          v-model="queryParams.log"
          placeholder="请输入日志"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="回调信息" prop="msg">
        <el-input
          v-model="queryParams.msg"
          placeholder="请输入回调信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单号" prop="no">
        <el-input
          v-model="queryParams.no"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="异步通知地址" prop="notifyUrl">
        <el-input
          v-model="queryParams.notifyUrl"
          placeholder="请输入异步通知地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单编号" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="站点" prop="station">
        <el-input
          v-model="queryParams.station"
          placeholder="请输入站点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务类型 1.入库 2.出库" prop="taskCategory">
        <el-input
          v-model="queryParams.taskCategory"
          placeholder="请输入任务类型 1.入库 2.出库"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="总管数" prop="tubesCount">
        <el-input
          v-model="queryParams.tubesCount"
          placeholder="请输入总管数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户 IP" prop="userIp">
        <el-input
          v-model="queryParams.userIp"
          placeholder="请输入用户 IP"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hv:task-order-detail:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hv:task-order-detail:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="描述信息" align="center" prop="body" />
      <el-table-column label="盒号" align="center" prop="boxCode" />
      <el-table-column label="日志" align="center" prop="log" />
      <el-table-column label="回调信息" align="center" prop="msg" />
      <el-table-column label="订单号" align="center" prop="no" />
      <el-table-column label="异步通知地址" align="center" prop="notifyUrl" />
      <el-table-column label="订单编号" align="center" prop="orderId" />
      <el-table-column label="站点" align="center" prop="station" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="任务类型 1.入库 2.出库" align="center" prop="taskCategory" />
      <el-table-column label="任务编码" align="center" prop="taskCode" />
      <el-table-column label="总管数" align="center" prop="tubesCount" />
      <el-table-column label="订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消" align="center" prop="type" />
      <el-table-column label="用户 IP" align="center" prop="userIp" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hv:task-order-detail:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hv:task-order-detail:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskOrderDetailForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskOrderDetailApi, TaskOrderDetailVO } from '@/api/dispatch/orderdetail'
import TaskOrderDetailForm from './TaskOrderDetailForm.vue'

/** 任务订单详情 列表 */
defineOptions({ name: 'TaskOrderDetail' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TaskOrderDetailVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  body: undefined,
  boxCode: undefined,
  log: undefined,
  msg: undefined,
  no: undefined,
  notifyUrl: undefined,
  orderId: undefined,
  station: undefined,
  status: undefined,
  taskCategory: undefined,
  taskCode: undefined,
  tubesCount: undefined,
  type: undefined,
  userIp: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskOrderDetailApi.getTaskOrderDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskOrderDetailApi.deleteTaskOrderDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskOrderDetailApi.exportTaskOrderDetail(queryParams)
    download.excel(data, '任务订单详情.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
