<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="描述信息" prop="body">
        <el-input v-model="formData.body" placeholder="请输入描述信息" />
      </el-form-item>
      <el-form-item label="盒号" prop="boxCode">
        <el-input v-model="formData.boxCode" placeholder="请输入盒号" />
      </el-form-item>
      <el-form-item label="日志" prop="log">
        <el-input v-model="formData.log" placeholder="请输入日志" />
      </el-form-item>
      <el-form-item label="回调信息" prop="msg">
        <el-input v-model="formData.msg" placeholder="请输入回调信息" />
      </el-form-item>
      <el-form-item label="订单号" prop="no">
        <el-input v-model="formData.no" placeholder="请输入订单号" />
      </el-form-item>
      <el-form-item label="异步通知地址" prop="notifyUrl">
        <el-input v-model="formData.notifyUrl" placeholder="请输入异步通知地址" />
      </el-form-item>
      <el-form-item label="订单编号" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="站点" prop="station">
        <el-input v-model="formData.station" placeholder="请输入站点" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="任务类型 1.入库 2.出库" prop="taskCategory">
        <el-input v-model="formData.taskCategory" placeholder="请输入任务类型 1.入库 2.出库" />
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input v-model="formData.taskCode" placeholder="请输入任务编码" />
      </el-form-item>
      <el-form-item label="总管数" prop="tubesCount">
        <el-input v-model="formData.tubesCount" placeholder="请输入总管数" />
      </el-form-item>
      <el-form-item label="订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消" prop="type">
        <el-select v-model="formData.type" placeholder="请选择订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户 IP" prop="userIp">
        <el-input v-model="formData.userIp" placeholder="请输入用户 IP" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TaskOrderDetailApi, TaskOrderDetailVO } from '@/api/dispatch/orderdetail'

/** 任务订单详情 表单 */
defineOptions({ name: 'TaskOrderDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  body: undefined,
  boxCode: undefined,
  log: undefined,
  msg: undefined,
  no: undefined,
  notifyUrl: undefined,
  orderId: undefined,
  station: undefined,
  status: undefined,
  taskCategory: undefined,
  taskCode: undefined,
  tubesCount: undefined,
  type: undefined,
  userIp: undefined
})
const formRules = reactive({
  notifyUrl: [{ required: true, message: '异步通知地址不能为空', trigger: 'blur' }],
  orderId: [{ required: true, message: '订单编号不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  tubesCount: [{ required: true, message: '总管数不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '订单类型 0初始化 1 待执行 2 待取货 3 AGV运行中 4 执行中 5 执行完成 6 异常 9 取消不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskOrderDetailApi.getTaskOrderDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskOrderDetailVO
    if (formType.value === 'create') {
      await TaskOrderDetailApi.createTaskOrderDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskOrderDetailApi.updateTaskOrderDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    body: undefined,
    boxCode: undefined,
    log: undefined,
    msg: undefined,
    no: undefined,
    notifyUrl: undefined,
    orderId: undefined,
    station: undefined,
    status: undefined,
    taskCategory: undefined,
    taskCode: undefined,
    tubesCount: undefined,
    type: undefined,
    userIp: undefined
  }
  formRef.value?.resetFields()
}
</script>
