<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="AGV编号" prop="carCode">
        <el-input v-model="formData.carCode" placeholder="请输入AGV编号" />
      </el-form-item>
      <el-form-item label="剩余电量" prop="carDumpEnergy">
        <el-input v-model="formData.carDumpEnergy" placeholder="请输入剩余电量" />
      </el-form-item>
      <el-form-item label="车辆故障报警信息" prop="carMsg">
        <el-input v-model="formData.carMsg" placeholder="请输入车辆故障报警信息" />
      </el-form-item>
      <el-form-item label="车体角度" prop="carOrientation">
        <el-input v-model="formData.carOrientation" placeholder="请输入车体角度" />
      </el-form-item>
      <el-form-item label="当前点" prop="carPos">
        <el-input v-model="formData.carPos" placeholder="请输入当前点" />
      </el-form-item>
      <el-form-item label="实时速度" prop="carSpeed">
        <el-input v-model="formData.carSpeed" placeholder="请输入实时速度" />
      </el-form-item>
      <el-form-item label="车辆运行状态" prop="carStatus">
        <el-radio-group v-model="formData.carStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="容器号、托盘号" prop="containerNo">
        <el-input v-model="formData.containerNo" placeholder="请输入容器号、托盘号" />
      </el-form-item>
      <el-form-item label="ip" prop="ip">
        <el-input v-model="formData.ip" placeholder="请输入ip" />
      </el-form-item>
      <el-form-item label="载货状态" prop="isLoad">
        <el-input v-model="formData.isLoad" placeholder="请输入载货状态" />
      </el-form-item>
      <el-form-item label="X坐标" prop="locationX">
        <el-input v-model="formData.locationX" placeholder="请输入X坐标" />
      </el-form-item>
      <el-form-item label="Y坐标" prop="locationY">
        <el-input v-model="formData.locationY" placeholder="请输入Y坐标" />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input v-model="formData.port" placeholder="请输入端口" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AgvStatusApi, AgvStatusVO } from '@/api/dispatch/agvstatus'

/** 设备agv状态 表单 */
defineOptions({ name: 'AgvStatusForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  carCode: undefined,
  carDumpEnergy: undefined,
  carMsg: undefined,
  carOrientation: undefined,
  carPos: undefined,
  carSpeed: undefined,
  carStatus: undefined,
  containerNo: undefined,
  ip: undefined,
  isLoad: undefined,
  locationX: undefined,
  locationY: undefined,
  port: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AgvStatusApi.getAgvStatus(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AgvStatusVO
    if (formType.value === 'create') {
      await AgvStatusApi.createAgvStatus(data)
      message.success(t('common.createSuccess'))
    } else {
      await AgvStatusApi.updateAgvStatus(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    carCode: undefined,
    carDumpEnergy: undefined,
    carMsg: undefined,
    carOrientation: undefined,
    carPos: undefined,
    carSpeed: undefined,
    carStatus: undefined,
    containerNo: undefined,
    ip: undefined,
    isLoad: undefined,
    locationX: undefined,
    locationY: undefined,
    port: undefined
  }
  formRef.value?.resetFields()
}
</script>
