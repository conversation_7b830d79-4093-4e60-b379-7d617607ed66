<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="AGV编号" prop="carCode">
        <el-input
          v-model="queryParams.carCode"
          placeholder="请输入AGV编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="剩余电量" prop="carDumpEnergy">
        <el-input
          v-model="queryParams.carDumpEnergy"
          placeholder="请输入剩余电量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="车辆故障报警信息" prop="carMsg">
        <el-input
          v-model="queryParams.carMsg"
          placeholder="请输入车辆故障报警信息"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="车体角度" prop="carOrientation">
        <el-input
          v-model="queryParams.carOrientation"
          placeholder="请输入车体角度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="当前点" prop="carPos">
        <el-input
          v-model="queryParams.carPos"
          placeholder="请输入当前点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="实时速度" prop="carSpeed">
        <el-input
          v-model="queryParams.carSpeed"
          placeholder="请输入实时速度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="车辆运行状态" prop="carStatus">
        <el-select
          v-model="queryParams.carStatus"
          placeholder="请选择车辆运行状态"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="容器号、托盘号" prop="containerNo">
        <el-input
          v-model="queryParams.containerNo"
          placeholder="请输入容器号、托盘号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="ip" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入ip"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="载货状态" prop="isLoad">
        <el-input
          v-model="queryParams.isLoad"
          placeholder="请输入载货状态"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="X坐标" prop="locationX">
        <el-input
          v-model="queryParams.locationX"
          placeholder="请输入X坐标"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Y坐标" prop="locationY">
        <el-input
          v-model="queryParams.locationY"
          placeholder="请输入Y坐标"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input
          v-model="queryParams.port"
          placeholder="请输入端口"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hv:agv-status:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hv:agv-status:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="AGV编号" align="center" prop="carCode" />
      <el-table-column label="剩余电量" align="center" prop="carDumpEnergy" />
      <el-table-column label="车辆故障报警信息" align="center" prop="carMsg" />
      <el-table-column label="车体角度" align="center" prop="carOrientation" />
      <el-table-column label="当前点" align="center" prop="carPos" />
      <el-table-column label="实时速度" align="center" prop="carSpeed" />
      <el-table-column label="车辆运行状态" align="center" prop="carStatus" />
      <el-table-column label="容器号、托盘号" align="center" prop="containerNo" />
      <el-table-column label="ip" align="center" prop="ip" />
      <el-table-column label="载货状态" align="center" prop="isLoad" />
      <el-table-column label="X坐标" align="center" prop="locationX" />
      <el-table-column label="Y坐标" align="center" prop="locationY" />
      <el-table-column label="端口" align="center" prop="port" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hv:agv-status:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hv:agv-status:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AgvStatusForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { AgvStatusApi, AgvStatusVO } from '@/api/dispatch/agvstatus'
import AgvStatusForm from './AgvStatusForm.vue'

/** 设备agv状态 列表 */
defineOptions({ name: 'AgvStatus' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AgvStatusVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  carCode: undefined,
  carDumpEnergy: undefined,
  carMsg: undefined,
  carOrientation: undefined,
  carPos: undefined,
  carSpeed: undefined,
  carStatus: undefined,
  containerNo: undefined,
  ip: undefined,
  isLoad: undefined,
  locationX: undefined,
  locationY: undefined,
  port: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AgvStatusApi.getAgvStatusPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AgvStatusApi.deleteAgvStatus(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AgvStatusApi.exportAgvStatus(queryParams)
    download.excel(data, '设备agv状态.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
