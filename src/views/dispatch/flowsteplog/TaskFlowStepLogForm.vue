<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="盒号" prop="boxCode">
        <el-input v-model="formData.boxCode" placeholder="请输入盒号" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="样本目的站点" prop="destination">
        <el-input v-model="formData.destination" placeholder="请输入样本目的站点" />
      </el-form-item>
      <el-form-item label="步骤结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="date"
          value-format="x"
          placeholder="选择步骤结束时间"
        />
      </el-form-item>
      <el-form-item label="执行方法" prop="executeMethod">
        <el-input v-model="formData.executeMethod" placeholder="请输入执行方法" />
      </el-form-item>
      <el-form-item label="执行对象" prop="executeObject">
        <el-input v-model="formData.executeObject" placeholder="请输入执行对象" />
      </el-form-item>
      <el-form-item label="步骤执行日志" prop="log">
        <el-input v-model="formData.log" placeholder="请输入步骤执行日志" />
      </el-form-item>
      <el-form-item label="流水号" prop="no">
        <el-input v-model="formData.no" placeholder="请输入流水号" />
      </el-form-item>
      <el-form-item label="任务ID，关联orderDetail表" prop="orderDetailId">
        <el-input v-model="formData.orderDetailId" placeholder="请输入任务ID，关联orderDetail表" />
      </el-form-item>
      <el-form-item label="任务ID，关联order表" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入任务ID，关联order表" />
      </el-form-item>
      <el-form-item label="订单类型 1.入库 2.出库" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择订单类型 1.入库 2.出库">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="重试次数" prop="retryCount">
        <el-input v-model="formData.retryCount" placeholder="请输入重试次数" />
      </el-form-item>
      <el-form-item label="扫描结果" prop="scanResult">
        <el-radio-group v-model="formData.scanResult">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="步骤开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          value-format="x"
          placeholder="选择步骤开始时间"
        />
      </el-form-item>
      <el-form-item label="步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="步骤名称" prop="stepName">
        <el-input v-model="formData.stepName" placeholder="请输入步骤名称" />
      </el-form-item>
      <el-form-item label="任务编号" prop="taskCode">
        <el-input v-model="formData.taskCode" placeholder="请输入任务编号" />
      </el-form-item>
      <el-form-item label="任务类型 1入 2 出" prop="taskType">
        <el-select v-model="formData.taskType" placeholder="请选择任务类型 1入 2 出">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="回调ID" prop="callbackId">
        <el-input v-model="formData.callbackId" placeholder="请输入回调ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TaskFlowStepLogApi, TaskFlowStepLogVO } from '@/api/dispatch/flowsteplog'

/** 任务步骤设备执行日志 表单 */
defineOptions({ name: 'TaskFlowStepLogForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  boxCode: undefined,
  description: undefined,
  destination: undefined,
  endTime: undefined,
  executeMethod: undefined,
  executeObject: undefined,
  log: undefined,
  no: undefined,
  orderDetailId: undefined,
  orderId: undefined,
  orderType: undefined,
  retryCount: undefined,
  scanResult: undefined,
  startTime: undefined,
  status: undefined,
  stepName: undefined,
  taskCode: undefined,
  taskType: undefined,
  callbackId: undefined
})
const formRules = reactive({
  orderId: [{ required: true, message: '任务ID，关联order表不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskFlowStepLogApi.getTaskFlowStepLog(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskFlowStepLogVO
    if (formType.value === 'create') {
      await TaskFlowStepLogApi.createTaskFlowStepLog(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskFlowStepLogApi.updateTaskFlowStepLog(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    boxCode: undefined,
    description: undefined,
    destination: undefined,
    endTime: undefined,
    executeMethod: undefined,
    executeObject: undefined,
    log: undefined,
    no: undefined,
    orderDetailId: undefined,
    orderId: undefined,
    orderType: undefined,
    retryCount: undefined,
    scanResult: undefined,
    startTime: undefined,
    status: undefined,
    stepName: undefined,
    taskCode: undefined,
    taskType: undefined,
    callbackId: undefined
  }
  formRef.value?.resetFields()
}
</script>
