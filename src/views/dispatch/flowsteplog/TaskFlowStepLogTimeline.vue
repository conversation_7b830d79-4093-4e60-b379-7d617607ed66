<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="700px">
    <div class="timeline-view">
      <div v-loading="loading" class="timeline-container">
        <div v-for="item in sortedList" :key="item.id" class="timeline-item">
          <div class="timeline-item-header">
            <div class="left-circle"></div>
            <div class="timeline-item-id">#{{ item.id }}</div>
            <div class="timeline-item-time">{{ formatDate(item.startTime) }}</div>
          </div>
          <div class="timeline-item-content">
            <div class="timeline-item-row">
              <span class="label">流水号:</span>
              <span>{{ item.no }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">执行方法:</span>
              <span>{{ item.executeMethod }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">盒号:</span>
              <span>{{ item.boxCode }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">步骤名称:</span>
              <span>{{ item.stepName }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">任务编号:</span>
              <span>{{ item.taskCode }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">目的站点:</span>
              <span>{{ item.destination }}</span>
            </div>
            <div class="timeline-item-row">
              <span class="label">步骤状态:</span>
              <span>{{ getStatusText(item.status) }}</span>
            </div>
            <div class="timeline-item-row" v-if="item.log">
              <span class="label">日志:</span>
              <span>{{ item.log }}</span>
            </div>
            <div class="timeline-item-row" v-if="item.callbackId">
              <span class="label">回调ID:</span>
              <span>{{ item.callbackId }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { TaskFlowStepLogApi, TaskFlowStepLogVO } from '@/api/dispatch/flowsteplog'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'TaskFlowStepLogTimeline' })

const dialogVisible = ref(false)
const dialogTitle = ref('任务流程步骤详情')
const loading = ref(false)
const list = ref<TaskFlowStepLogVO[]>([])

// 按ID从小到大排序的数据
const sortedList = computed(() => {
  return [...list.value].sort((a, b) => a.id - b.id)
})

// 根据状态值获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '未开始',
    1: '执行中',
    2: '已完成',
    3: '执行失败',
    4: '已终止',
    5: '已处理',
    6: '处理中'
  }
  return statusMap[status] || '未知状态'
}

// 根据no获取数据
const getDataByNo = async (no: string) => {
  if (!no) return

  loading.value = true
  try {
    // 查询与指定no关联的所有记录
    const params = {
      no: no,
      pageNo: 1,
      pageSize: 100 // 假设不会超过100条记录
    }
    const result = await TaskFlowStepLogApi.getTaskFlowStepLogPage(params)
    list.value = result.list || []
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 打开弹窗
const open = async (no: string) => {
  dialogVisible.value = true
  await getDataByNo(no)
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.timeline-view {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px 0;
}

.timeline-container {
  position: relative;
  padding-left: 20px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 10px;
    width: 2px;
    height: 100%;
    background-color: #e0e0e0;
  }
}

.timeline-item {
  margin-bottom: 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.left-circle {
  position: absolute;
  left: -14px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #409EFF;
  border: 2px solid white;
  z-index: 1;
}

.timeline-item-id {
  font-weight: bold;
  margin-right: 15px;
}

.timeline-item-time {
  color: #909399;
  font-size: 13px;
}

.timeline-item-content {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.timeline-item-row {
  display: flex;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  width: 80px;
  color: #606266;
  font-weight: 500;
}
</style>
