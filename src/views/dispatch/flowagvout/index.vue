<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="盒号" prop="boxCode">
        <el-input
          v-model="queryParams.boxCode"
          placeholder="请输入盒号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="样本目的站点" prop="destination">
        <el-input
          v-model="queryParams.destination"
          placeholder="请输入样本目的站点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="执行方法" prop="executeMethod">
        <el-input
          v-model="queryParams.executeMethod"
          placeholder="请输入执行方法"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="执行对象" prop="executeObject">
        <el-input
          v-model="queryParams.executeObject"
          placeholder="请输入执行对象"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="步骤执行日志" prop="log">
        <el-input
          v-model="queryParams.log"
          placeholder="请输入步骤执行日志"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流水号" prop="no">
        <el-input
          v-model="queryParams.no"
          placeholder="请输入流水号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单类型 1.入库 2.出库" prop="orderType">
        <el-select
          v-model="queryParams.orderType"
          placeholder="请选择订单类型 1.入库 2.出库"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="重试次数" prop="retryCount">
        <el-input
          v-model="queryParams.retryCount"
          placeholder="请输入重试次数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="扫描结果" prop="scanResult">
        <el-select
          v-model="queryParams.scanResult"
          placeholder="请选择扫描结果"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务编号" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hv:task-flow-agv-out:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hv:task-flow-agv-out:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="盒号" align="center" prop="boxCode" />
      <el-table-column label="样本目的站点" align="center" prop="destination" />
      <el-table-column label="执行方法" align="center" prop="executeMethod" />
      <el-table-column label="执行对象" align="center" prop="executeObject" />
      <el-table-column label="步骤执行日志" align="center" prop="log" />
      <el-table-column label="流水号" align="center" prop="no" />
      <el-table-column label="订单类型 1.入库 2.出库" align="center" prop="orderType" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="重试次数" align="center" prop="retryCount" />
      <el-table-column label="扫描结果" align="center" prop="scanResult" />
      <el-table-column label="步骤状态，0.未开始 1.执行中 2.已完成,3.执行失败,4.已终止 5.已处理 6.处理中" align="center" prop="status" />
      <el-table-column label="任务编号" align="center" prop="taskCode" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openTimeline(scope.row.no)"
            v-hasPermi="['hv:task-flow-agv-out:update']"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskFlowAgvOutForm ref="formRef" @success="getList" />

  <!-- 时间线弹窗 -->
  <TaskFlowAgvOutTimeline ref="timelineRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskFlowAgvOutApi, TaskFlowAgvOutVO } from '@/api/dispatch/flowagvout'
import TaskFlowAgvOutForm from './TaskFlowAgvOutForm.vue'
import TaskFlowAgvOutTimeline from './TaskFlowAgvOutTimeline.vue'

/** agv大库出库盒子状态记录 列表 */
defineOptions({ name: 'TaskFlowAgvOut' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TaskFlowAgvOutVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  boxCode: undefined,
  destination: undefined,
  executeMethod: undefined,
  executeObject: undefined,
  log: undefined,
  no: undefined,
  orderType: undefined,
  remark: undefined,
  retryCount: undefined,
  scanResult: undefined,
  status: undefined,
  taskCode: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 时间线组件的引用
const timelineRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskFlowAgvOutApi.getTaskFlowAgvOutPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskFlowAgvOutApi.deleteTaskFlowAgvOut(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskFlowAgvOutApi.exportTaskFlowAgvOut(queryParams)
    download.excel(data, 'agv大库出库盒子状态记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开时间线视图 */
const openTimeline = (no: string) => {
  timelineRef.value.open(no)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
