<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    v-loading="formLoading"
    class="task-job-form"
  >
    <!-- 基本信息 -->
    <el-card class="mb-4">
      <template #header>
        <span>基本信息</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务编码" prop="taskCode">
            <el-input v-model="formData.taskCode" placeholder="请输入任务编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编码" prop="deviceCode">
            <el-input v-model="formData.deviceCode" placeholder="请输入设备编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否启用AGV" prop="enabledAgv">
            <el-radio-group v-model="formData.enabledAgv">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务状态" prop="taskStatus">
            <el-select v-model="formData.taskStatus" placeholder="请选择任务状态">
              <el-option label="待执行" :value="1" />
              <el-option label="执行中" :value="2" />
              <el-option label="已完成" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务类别" prop="taskCategory">
            <el-select v-model="formData.taskCategory" placeholder="请选择任务类别">
              <el-option label="存储" :value="1" />
              <el-option label="取出" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预约时间" prop="reserveTime">
            <el-date-picker
              v-model="formData.reserveTime"
              type="datetime"
              placeholder="选择预约时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="扩展信息" prop="extInfo">
        <el-input
          v-model="formData.extInfo"
          type="textarea"
          :rows="3"
          placeholder="请输入扩展信息"
        />
      </el-form-item>
    </el-card>

    <!-- 盒子列表 -->
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span>盒子列表</span>
          <el-button type="primary" @click="addBox">添加盒子</el-button>
        </div>
      </template>

      <div v-for="(box, boxIndex) in formData.boxList" :key="boxIndex" class="box-item mb-4">
        <el-card>
          <template #header>
            <div class="flex justify-between items-center">
              <span>盒子 {{ boxIndex + 1 }}</span>
              <el-button type="danger" size="small" @click="removeBox(boxIndex)">删除盒子</el-button>
            </div>
          </template>

          <el-row :gutter="20" class="mb-4">
            <el-col :span="8">
              <el-form-item :label="`盒号`" :prop="`boxList.${boxIndex}.boxCode`" :rules="[{ required: true, message: '盒号不能为空', trigger: 'blur' }]">
                <el-input v-model="box.boxCode" placeholder="请输入盒号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="`位置编码`" :prop="`boxList.${boxIndex}.locationCode`" :rules="[{ required: true, message: '位置编码不能为空', trigger: 'blur' }]">
                <el-input v-model="box.locationCode" placeholder="请输入位置编码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="`耗材编码`" :prop="`boxList.${boxIndex}.consumableCode`" :rules="[{ required: true, message: '耗材编码不能为空', trigger: 'blur' }]">
                <el-input v-model="box.consumableCode" placeholder="请输入耗材编码" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 试管列表 -->
          <div class="tube-section">
            <div class="flex justify-between items-center mb-3">
              <span class="font-medium">试管列表</span>
              <el-button type="primary" size="small" @click="addTube(boxIndex)">添加试管</el-button>
            </div>

            <div v-if="box.tubeList.length === 0" class="text-center text-gray-500 py-4">
              暂无试管，请点击"添加试管"按钮添加
            </div>

            <div v-else class="tube-list">
              <el-row :gutter="10" class="tube-header mb-2">
                <el-col :span="6"><strong>试管编码</strong></el-col>
                <el-col :span="6"><strong>坐标</strong></el-col>
                <el-col :span="6"><strong>位置编码</strong></el-col>
                <el-col :span="6"><strong>操作</strong></el-col>
              </el-row>

              <div v-for="(tube, tubeIndex) in box.tubeList" :key="tubeIndex" class="tube-row mb-2">
                <el-row :gutter="10">
                  <el-col :span="6">
                    <el-form-item :prop="`boxList.${boxIndex}.tubeList.${tubeIndex}.tubeCode`" :rules="[{ required: true, message: '试管编码不能为空', trigger: 'blur' }]">
                      <el-input v-model="tube.tubeCode" placeholder="试管编码" size="small" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :prop="`boxList.${boxIndex}.tubeList.${tubeIndex}.coordinate`">
                      <el-input v-model="tube.coordinate" placeholder="坐标" size="small" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :prop="`boxList.${boxIndex}.tubeList.${tubeIndex}.locationCode`" :rules="[{ required: true, message: '位置编码不能为空', trigger: 'blur' }]">
                      <el-input v-model="tube.locationCode" placeholder="位置编码" size="small" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="danger" size="small" @click="removeTube(boxIndex, tubeIndex)">删除</el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <div v-if="formData.boxList.length === 0" class="text-center text-gray-500 py-8">
        暂无盒子，请点击"添加盒子"按钮添加
      </div>
    </el-card>

    <!-- 提交按钮 -->
    <div class="text-center">
      <el-button @click="resetForm" :disabled="formLoading">重置</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">提交任务</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { FreezerApi, SendTaskJobVO, BoxVO, TubeVO } from '@/api/dispatch/freezer'

/** 任务作业表单 */
defineOptions({ name: 'TaskJobForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const formLoading = ref(false) // 表单的加载中
const formData = ref<SendTaskJobVO>({
  boxList: [],
  extInfo: null,
  taskCode: '',
  deviceCode: '',
  enabledAgv: 0,
  taskStatus: 1,
  reserveTime: null,
  taskCategory: 1
})

const formRules = reactive({
  taskCode: [{ required: true, message: '任务编码不能为空', trigger: 'blur' }],
  deviceCode: [{ required: true, message: '设备编码不能为空', trigger: 'blur' }],
  enabledAgv: [{ required: true, message: '请选择是否启用AGV', trigger: 'change' }],
  taskStatus: [{ required: true, message: '请选择任务状态', trigger: 'change' }],
  taskCategory: [{ required: true, message: '请选择任务类别', trigger: 'change' }]
})

const formRef = ref() // 表单 Ref

/** 添加盒子 */
const addBox = () => {
  const newBox: BoxVO = {
    boxCode: '',
    tubeList: [],
    locationCode: '',
    consumableCode: ''
  }
  formData.value.boxList.push(newBox)
}

/** 删除盒子 */
const removeBox = (index: number) => {
  formData.value.boxList.splice(index, 1)
}

/** 添加试管 */
const addTube = (boxIndex: number) => {
  const box = formData.value.boxList[boxIndex]
  const newTube: TubeVO = {
    boxCode: box.boxCode,
    tubeCode: '',
    coordinate: null,
    locationCode: box.locationCode
  }
  box.tubeList.push(newTube)
}

/** 删除试管 */
const removeTube = (boxIndex: number, tubeIndex: number) => {
  formData.value.boxList[boxIndex].tubeList.splice(tubeIndex, 1)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 校验至少有一个盒子
  if (formData.value.boxList.length === 0) {
    message.error('请至少添加一个盒子')
    return
  }

  // 校验每个盒子至少有一个试管
  for (let i = 0; i < formData.value.boxList.length; i++) {
    if (formData.value.boxList[i].tubeList.length === 0) {
      message.error(`盒子 ${i + 1} 至少需要添加一个试管`)
      return
    }
  }

  // 同步盒号到试管
  formData.value.boxList.forEach(box => {
    box.tubeList.forEach(tube => {
      tube.boxCode = box.boxCode
      if (!tube.locationCode) {
        tube.locationCode = box.locationCode
      }
    })
  })

  // 提交请求
  formLoading.value = true
  try {
    await FreezerApi.sendTaskJob(formData.value)
    message.success('任务提交成功')
    // 发送操作成功的事件
    emit('success')
    resetForm()
  } catch (error) {
    console.error('任务提交失败:', error)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    boxList: [],
    extInfo: null,
    taskCode: '',
    deviceCode: '',
    enabledAgv: 0,
    taskStatus: 1,
    reserveTime: null,
    taskCategory: 1
  }
  formRef.value?.resetFields()
}

// 初始化时添加一个空盒子
onMounted(() => {
  addBox()
})
</script>

<style lang="scss" scoped>
.task-job-form {
  max-width: 1200px;
  margin: 0 auto;
}

.box-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.tube-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
}

.tube-header {
  background-color: #e9ecef;
  padding: 8px;
  border-radius: 4px;
}

.tube-row {
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.tube-list {
  max-height: 400px;
  overflow-y: auto;
}
</style>
