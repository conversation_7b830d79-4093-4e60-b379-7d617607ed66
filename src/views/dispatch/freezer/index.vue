<template>
  <ContentWrap>
    <!-- 页面标题 -->
    <div class="mb-4">
      <h2>任务新建</h2>
      <p class="text-gray-500">创建新的冷冻器任务作业</p>
    </div>

    <!-- 任务新建表单 -->
    <TaskJobForm ref="formRef" @success="handleSuccess" />
  </ContentWrap>
</template>

<script setup lang="ts">
import TaskJobForm from './TaskJobForm.vue'

/** 任务新建页面 */
defineOptions({ name: 'FreezerTaskCreate' })

const message = useMessage() // 消息弹窗
const { push } = useRouter() // 路由

const formRef = ref()

/** 成功回调 */
const handleSuccess = () => {
  message.success('任务创建成功')
  // 可以选择跳转到任务列表页面或其他页面
  // push('/dispatch/order')
}

// 页面加载时初始化表单
onMounted(() => {
  // 可以在这里进行一些初始化操作
})
</script>

<style lang="scss" scoped>
// 页面样式
</style>
