<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="报警编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入报警编码" />
      </el-form-item>
      <el-form-item label="报警处理" prop="deal">
        <el-input v-model="formData.deal" placeholder="请输入报警处理" />
      </el-form-item>
      <el-form-item label="报警设备" prop="equipment">
        <el-input v-model="formData.equipment" placeholder="请输入报警设备" />
      </el-form-item>
      <el-form-item label="报警级别" prop="level">
        <el-input v-model="formData.level" placeholder="请输入报警级别" />
      </el-form-item>
      <el-form-item label="报警信息" prop="message">
        <el-input v-model="formData.message" placeholder="请输入报警信息" />
      </el-form-item>
      <el-form-item label="报警名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入报警名称" />
      </el-form-item>
      <el-form-item label="报警时间" prop="time">
        <el-date-picker
          v-model="formData.alarmTime"
          type="datetime"
          value-format="x"
          placeholder="选择报警时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IotAlarmApi, IotAlarmVO } from '@/api/dispatch/alarm'

/** 设备报警上传信息 表单 */
defineOptions({ name: 'IotAlarmForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  deal: undefined,
  equipment: undefined,
  level: undefined,
  message: undefined,
  name: undefined,
  alarmTime: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await IotAlarmApi.getIotAlarm(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IotAlarmVO
    if (formType.value === 'create') {
      await IotAlarmApi.createIotAlarm(data)
      message.success(t('common.createSuccess'))
    } else {
      await IotAlarmApi.updateIotAlarm(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    deal: undefined,
    equipment: undefined,
    level: undefined,
    message: undefined,
    name: undefined,
    alarmTime: undefined
  }
  formRef.value?.resetFields()
}
</script>
