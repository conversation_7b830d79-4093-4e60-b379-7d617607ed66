<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="盒子号" prop="boxCode">
        <el-input v-model="formData.boxCode" placeholder="请输入盒子号" />
      </el-form-item>
      <el-form-item label="回调参数" prop="callbackParam">
        <el-input v-model="formData.callbackParam" placeholder="请输入回调参数" />
      </el-form-item>
      <el-form-item label="回调结果" prop="callbackResult">
        <el-input v-model="formData.callbackResult" placeholder="请输入回调结果" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="订单号" prop="no">
        <el-input v-model="formData.no" placeholder="请输入订单号" />
      </el-form-item>
      <el-form-item label="异步通知地址" prop="notifyUrl">
        <el-input v-model="formData.notifyUrl" placeholder="请输入异步通知地址" />
      </el-form-item>
      <el-form-item label="订单编号" prop="orderDetailId">
        <el-input v-model="formData.orderDetailId" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input v-model="formData.taskCode" placeholder="请输入任务编码" />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="formData.taskType" placeholder="请选择任务类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单编号集合" prop="orderDetailIds">
        <el-input v-model="formData.orderDetailIds" placeholder="请输入订单编号集合" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TaskOrderDetailCallbackApi, TaskOrderDetailCallbackVO } from '@/api/dispatch/orderdetailcallback'

/** 任务订单详情日志 表单 */
defineOptions({ name: 'TaskOrderDetailCallbackForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  boxCode: undefined,
  callbackParam: undefined,
  callbackResult: undefined,
  description: undefined,
  no: undefined,
  notifyUrl: undefined,
  orderDetailId: undefined,
  status: undefined,
  taskCode: undefined,
  taskType: undefined,
  orderDetailIds: undefined
})
const formRules = reactive({
  notifyUrl: [{ required: true, message: '异步通知地址不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskOrderDetailCallbackApi.getTaskOrderDetailCallback(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskOrderDetailCallbackVO
    if (formType.value === 'create') {
      await TaskOrderDetailCallbackApi.createTaskOrderDetailCallback(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskOrderDetailCallbackApi.updateTaskOrderDetailCallback(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    boxCode: undefined,
    callbackParam: undefined,
    callbackResult: undefined,
    description: undefined,
    no: undefined,
    notifyUrl: undefined,
    orderDetailId: undefined,
    status: undefined,
    taskCode: undefined,
    taskType: undefined,
    orderDetailIds: undefined
  }
  formRef.value?.resetFields()
}
</script>
