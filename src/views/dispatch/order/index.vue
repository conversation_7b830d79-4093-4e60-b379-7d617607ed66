<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="是否agv" prop="enabledAgv">
        <el-input
          v-model="queryParams.enabledAgv"
          placeholder="请输入是否agv 1：是 0：否"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否缺盒" prop="isLack">
        <el-input
          v-model="queryParams.isLack"
          placeholder="请输入是否缺盒 1：缺盒 0：不缺盒"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select
          v-model="queryParams.orderType"
          placeholder="请选择订单类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="站点" prop="station">
        <el-input
          v-model="queryParams.station"
          placeholder="请输入站点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hv:task-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hv:task-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :show-overflow-tooltip="true"
      :stripe="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="设备描述" align="center" prop="deviceDesc" width="180px" />
      <el-table-column label="是否agv" align="center" prop="enabledAgv">
        <template #default="scope">
          <el-tag :type="scope.row.enabledAgv === 1 ? 'success' : 'warning'">
            {{ scope.row.enabledAgv === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否缺盒" align="center" prop="isLack">
        <template #default="scope">
          <el-tag :type="scope.row.isLack === 1 ? 'success' : 'warning'">
            {{ scope.row.isLack === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单号" align="center" prop="no" width="180px" />
      <el-table-column label="订单类型" align="center" prop="orderType">
        <template #default="scope">
          <el-tag :type="getOrderTypeTagType(scope.row.orderType)">
            {{ getOrderTypeLabel(scope.row.orderType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务编码" align="center" prop="taskCode" width="280px" />
      <el-table-column label="站点" align="center" prop="station" width="180px" />
      <el-table-column label="状态" align="center" prop="status" width="120px">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{
            getStatusLabel(scope.row.status)
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务编码" align="center" prop="subject" width="180px" />
      <el-table-column label="总管数" align="center" prop="tubesCount" />
      <!--      <el-table-column label="用户 IP" align="center" prop="userIp" />-->
      <el-table-column label="总盒数" align="center" prop="boxCount" />
      <el-table-column label="已执行盒数" align="center" prop="executedBoxCount" />
      <el-table-column label="已执行管数" align="center" prop="executedTubesCount" />
      <el-table-column
        label="任务完成时间"
        align="center"
        prop="finishTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="描述信息" align="center" prop="body" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!--          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hv:task-order:update']"
          >
            编辑
          </el-button>-->
          <!--          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hv:task-order:delete']"
          >
            删除
          </el-button>-->
          <el-button
            link
            type="danger"
            @click="handleClose(scope.row.id)"
            v-hasPermi="['hv:task-order:update']"
          >
            关闭订单
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <Demo03StudentForm ref="formRef" @success="getList" />
  <!-- 子表的列表 -->
  <ContentWrap>
    <el-tabs model-value="taskOrderDetail">
      <el-tab-pane label="订单详情" name="taskOrderDetail">
        <TaskOrderDetailList :order-id="currentRow?.id" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import TaskOrderDetailList from './components/TaskOrderDetailList.vue'
import { TaskOrderApi, TaskOrderVO } from '@/api/dispatch/order'

defineOptions({ name: 'Demo03Student' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TaskOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  body: undefined,
  boxCount: undefined,
  deviceDesc: undefined,
  enabledAgv: undefined,
  executedBoxCount: undefined,
  executedTubesCount: undefined,
  finishTime: [],
  isLack: undefined,
  no: undefined,
  orderType: undefined,
  station: undefined,
  status: undefined,
  subject: undefined,
  taskCode: undefined,
  tubesCount: undefined,
  userIp: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskOrderApi.getTaskOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskOrderApi.deleteTaskOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/**
 * 关闭按钮
 */
const handleClose = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskOrderApi.closeTaskOrder(id)
    message.success(t('关闭成功'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskOrderApi.exportTaskOrder(queryParams)
    download.excel(data, '任务订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref<TaskOrderVO | null>(null) // 选中行
const handleCurrentChange = (row) => {
  currentRow.value = row
}

/** 获取状态标签 */
const getStatusLabel = (status) => {
  switch (Number(status)) {
    case 0:
      return '待执行'
    case 100:
      return '执行中'
    case 200:
      return '成功'
    case 500:
      return '程序异常'
    case 400:
      return '扫码失败'
    case 300:
      return '关闭'
    case 600:
      return '挑管中'
    case 900:
      return '异常关闭'
    case 700:
      return '执行中'
    case 99:
      return '挂起'
    default:
      return `未知状态(${status})`
  }
}

/** 获取状态类型 */
const getStatusType = (status) => {
  switch (Number(status)) {
    case 0:
      return 'info'
    case 100:
      return 'primary'
    case 200:
      return 'success'
    case 500:
      return 'danger'
    case 400:
      return 'danger'
    case 300:
      return 'info'
    case 600:
      return 'warning'
    case 900:
      return 'danger'
    case 700:
      return 'primary'
    case 99:
      return 'warning'
    default:
      return 'info'
  }
}

/** 获取订单类型标签 */
const getOrderTypeLabel = (orderType) => {
  switch (Number(orderType)) {
    case 1:
      return '入库'
    case 2:
      return '出库'
    case 9:
      return '预约出库'
    default:
      return `未知(${orderType})`
  }
}

/** 获取订单类型标签样式 */
const getOrderTypeTagType = (orderType) => {
  switch (Number(orderType)) {
    case 1:
      return 'success'
    case 2:
      return 'primary'
    case 9:
      return 'warning'
    default:
      return 'info'
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
