<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="描述信息" align="center" prop="body" />
      <el-table-column
        label="盒号"
        align="center"
        prop="boxCode"
        width="180px"
      />
      <el-table-column label="订单类型" align="center" prop="type" />
      <el-table-column label="订单号" align="center" prop="no" width="180px"/>
      <el-table-column label="订单编号" align="center" prop="orderId" width="180px"/>
      <el-table-column label="站点" align="center" prop="station" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="任务类型" align="center" prop="taskCategory" />
      <el-table-column label="任务编码" align="center" prop="taskCode" width="180px"/>
      <el-table-column label="总管数" align="center" prop="tubesCount" />
      <el-table-column label="异步通知地址" align="center" prop="notifyUrl" />
      <el-table-column label="日志" align="center" prop="log" />
      <el-table-column label="回调信息" align="center" prop="msg" />
      <el-table-column label="用户 IP" align="center" prop="userIp" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { TaskOrderDetailApi, TaskOrderDetailVO } from '@/api/dispatch/orderdetail'

/** 任务订单详情 列表 */
defineOptions({ name: 'TaskOrderDetail' })

const props = defineProps<{
  orderId?: number //（主表的关联字段）
}>()
const loading = ref(false) // 列表的加载中
const list = ref<TaskOrderDetailVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined as unknown,
})

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.orderId,
  (val: number) => {
    if (!val) {
      return
    }
    queryParams.orderId = val
    handleQuery()
  },
  { immediate: true, deep: true }
)


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskOrderDetailApi.getTaskOrderDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
</script>
