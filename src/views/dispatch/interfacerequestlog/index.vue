<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="接口名称" prop="apiName">
        <el-input
          v-model="queryParams.apiName"
          placeholder="请输入接口名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="消费状态" prop="consumeStatus">
        <el-select
          v-model="queryParams.consumeStatus"
          placeholder="请选择消费状态 1：消费成功 2：消费失败"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否缺盒" prop="isLack">
        <el-input
          v-model="queryParams.isLack"
          placeholder="请输入是否缺盒 1：缺盒 0：不缺盒"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="推送状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择推送状态 1：推送成功 2：推送失败"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select
          v-model="queryParams.taskStatus"
          placeholder="请选择任务状态，1.下发 2.重试 4.已取消"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['dispatch:interface-request-log:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>-->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['dispatch:interface-request-log:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号，唯一自增" align="center" prop="id" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="接口名称" align="center" prop="apiName" />
      <el-table-column label="消费日志" align="center" prop="consumeLog" />
      <el-table-column label="消费状态 1：消费成功 2：消费失败" align="center" prop="consumeStatus" />
      <el-table-column label="异常信息" align="center" prop="exceptionInfo" />
      <el-table-column label="请求host" align="center" prop="host" />
      <el-table-column label="是否缺盒 1：缺盒 0：不缺盒" align="center" prop="isLack" />
      <el-table-column label="no" align="center" prop="no" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="请求key" align="center" prop="requestKey" />
      <el-table-column label="请求参数" align="center" prop="requestParam" />
      <el-table-column label="响应参数" align="center" prop="responseParam" />
      <el-table-column label="推送状态 1：推送成功 2：推送失败" align="center" prop="status" />
      <el-table-column label="任务状态，1.下发 2.重试 4.已取消" align="center" prop="taskStatus" />
      <el-table-column label="唯一标识" align="center" prop="uniqueId" />
      <el-table-column label="请求url" align="center" prop="url" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['dispatch:interface-request-log:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['dispatch:interface-request-log:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InterfaceRequestLogForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InterfaceRequestLogApi, InterfaceRequestLogVO } from '@/api/dispatch/interfacerequestlog'
import InterfaceRequestLogForm from './InterfaceRequestLogForm.vue'

/** 接口请求日志 列表 */
defineOptions({ name: 'InterfaceRequestLog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InterfaceRequestLogVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  createTime: [],
  apiName: undefined,
  consumeLog: undefined,
  consumeStatus: undefined,
  exceptionInfo: undefined,
  host: undefined,
  isLack: undefined,
  no: undefined,
  remark: undefined,
  requestKey: undefined,
  requestParam: undefined,
  responseParam: undefined,
  status: undefined,
  taskStatus: undefined,
  uniqueId: undefined,
  url: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InterfaceRequestLogApi.getInterfaceRequestLogPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InterfaceRequestLogApi.deleteInterfaceRequestLog(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InterfaceRequestLogApi.exportInterfaceRequestLog(queryParams)
    download.excel(data, '接口请求日志.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
