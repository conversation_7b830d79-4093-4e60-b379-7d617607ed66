<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="接口名称" prop="apiName">
        <el-input v-model="formData.apiName" placeholder="请输入接口名称" />
      </el-form-item>
      <el-form-item label="消费日志" prop="consumeLog">
        <el-input v-model="formData.consumeLog" placeholder="请输入消费日志" />
      </el-form-item>
      <el-form-item label="消费状态 1：消费成功 2：消费失败" prop="consumeStatus">
        <el-radio-group v-model="formData.consumeStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="异常信息" prop="exceptionInfo">
        <el-input v-model="formData.exceptionInfo" placeholder="请输入异常信息" />
      </el-form-item>
      <el-form-item label="请求host" prop="host">
        <el-input v-model="formData.host" placeholder="请输入请求host" />
      </el-form-item>
      <el-form-item label="是否缺盒 1：缺盒 0：不缺盒" prop="isLack">
        <el-input v-model="formData.isLack" placeholder="请输入是否缺盒 1：缺盒 0：不缺盒" />
      </el-form-item>
      <el-form-item label="no" prop="no">
        <el-input v-model="formData.no" placeholder="请输入no" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="请求key" prop="requestKey">
        <el-input v-model="formData.requestKey" placeholder="请输入请求key" />
      </el-form-item>
      <el-form-item label="请求参数" prop="requestParam">
        <el-input v-model="formData.requestParam" placeholder="请输入请求参数" />
      </el-form-item>
      <el-form-item label="响应参数" prop="responseParam">
        <el-input v-model="formData.responseParam" placeholder="请输入响应参数" />
      </el-form-item>
      <el-form-item label="推送状态 1：推送成功 2：推送失败" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="任务状态，1.下发 2.重试 4.已取消" prop="taskStatus">
        <el-radio-group v-model="formData.taskStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="唯一标识" prop="uniqueId">
        <el-input v-model="formData.uniqueId" placeholder="请输入唯一标识" />
      </el-form-item>
      <el-form-item label="请求url" prop="url">
        <el-input v-model="formData.url" placeholder="请输入请求url" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InterfaceRequestLogApi, InterfaceRequestLogVO } from '@/api/dispatch/interfacerequestlog'

/** 接口请求日志 表单 */
defineOptions({ name: 'InterfaceRequestLogForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  apiName: undefined,
  consumeLog: undefined,
  consumeStatus: undefined,
  exceptionInfo: undefined,
  host: undefined,
  isLack: undefined,
  no: undefined,
  remark: undefined,
  requestKey: undefined,
  requestParam: undefined,
  responseParam: undefined,
  status: undefined,
  taskStatus: undefined,
  uniqueId: undefined,
  url: undefined
})
const formRules = reactive({
  isLack: [{ required: true, message: '是否缺盒 1：缺盒 0：不缺盒不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InterfaceRequestLogApi.getInterfaceRequestLog(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InterfaceRequestLogVO
    if (formType.value === 'create') {
      await InterfaceRequestLogApi.createInterfaceRequestLog(data)
      message.success(t('common.createSuccess'))
    } else {
      await InterfaceRequestLogApi.updateInterfaceRequestLog(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    apiName: undefined,
    consumeLog: undefined,
    consumeStatus: undefined,
    exceptionInfo: undefined,
    host: undefined,
    isLack: undefined,
    no: undefined,
    remark: undefined,
    requestKey: undefined,
    requestParam: undefined,
    responseParam: undefined,
    status: undefined,
    taskStatus: undefined,
    uniqueId: undefined,
    url: undefined
  }
  formRef.value?.resetFields()
}
</script>
